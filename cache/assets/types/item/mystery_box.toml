# Mystery box
[[item]]
inherit=6199
name="Mystery Box"
op1="Open"
grandexchange=true

# Donator Mystery Box
[[item]]
id=32231
inherit=6199
name="Donator Box"
placeholderid=32233
invmodel=60264
stackable=0
notedid=32232
originalcolours=[9125, 919]
replacementcolours=[20815, 55067]

# Donator Mystery Box (noted)
[[item]]
id=32232
inherit=6200
notedid=32231
notedtemplate=799
originalcolours=[9125, 919]
replacementcolours=[20815, 55067]

# Donator Mystery Chest
[[item]]
id=32203
inherit=6199
name="Donator Chest"
placeholderid=32205
invmodel=60204
stackable=0
zoom=2040
modelroll=1883
modelpitch=114
modelyaw=26
notedid=32204
originalcolours=[6442]
replacementcolours=[55067]

# Donator Mystery Chest (noted)
[[item]]
id=32204
inherit=6200
notedid=32203
op1=""
op2=""
op3=""
op4=""
op5="Drop"
groundops=["", "", "Take", "", ""]
notedtemplate=799
invmodel=60067
originalcolours=[6442]
replacementcolours=[55067]

# Donator Mystery Chest (placeholder)
[[item]]
id=32205
inherit=6199
name="Donator Chest"
placeholderid=32203
placeholdertemplate=14401
invmodel=60204
originalcolours=[6442]
replacementcolours=[55067]

# Advanced Donator Mystery Box
[[item]]
inherit=6199
id=32162
name="Advanced Donator Box"
invmodel=60152
grandexchange=true
placeholderid=33162
originalcolours=[112, 18206, 18203]
replacementcolours=[55067, 20815, 20815]

# Advanced Donator Mystery Box (placeholder)
[[item]]
inherit=16551
id=33162
placeholderid=32162
placeholdertemplate=14401
originalcolours=[112, 18206, 18203]
replacementcolours=[55067, 20815, 20815]

# Advanced Donator Mystery Chest
[[item]]
inherit=6199
id=32165
name="Advanced Donator Chest"
invmodel=60204
stackable=0
zoom=2040
modelroll=1883
modelpitch=114
modelyaw=26
notedid=32204
grandexchange=true
placeholderid=33165
originalcolours=[6442]
replacementcolours=[20815]

# Advanced Donator Mystery Chest (placeholder)
[[item]]
inherit=16551
id=33165
invmodel=60204
placeholderid=32165
placeholdertemplate=14401
originalcolours=[6442]
replacementcolours=[20815]

# Donator Weapon Box
[[item]]
id=32206
inherit=6199
name="Donator Weapon Box"
placeholderid=32208
invmodel=60203
stackable=0
notedid=32207

# Donator Weapon Box (noted)
[[item]]
id=32207
inherit=6200
name="Donator Weapon Box"
notedid=32206
op1=""
op2=""
op3=""
op4=""
op5="Drop"
groundops=["", "", "Take", "", ""]
notedtemplate=799
invmodel=60067

# Donator Weapon Box (placeholder)
[[item]]
id=32208
inherit=6199
name="Donator Weapon Box"
placeholderid=32206
placeholdertemplate=14401
invmodel=60203

# Donator Armour Box
[[item]]
inherit=6199
id=32164
name="Donator Armour Box"
invmodel=60150
grandexchange=true
placeholderid=33164

# Donator Armour Box (placeholder)
[[item]]
inherit=16551
id=33164
placeholderid=32164
placeholdertemplate=14401

# Holiday Mystery Box
[[item]]
id=32080
inherit=6199
name="Holiday Box"
placeholderid=33080
invmodel=60067
stackable=0
zoom=780
modelroll=861
modelpitch=148
modelyaw=26
offsetx=-1
offsety=-4
notedid=32081

# Holiday Mystery Box
[[item]]
id=32081
inherit=6200
name="Holiday Box"
notedid=32080
op1=""
op2=""
op3=""
op4=""
op5="Drop"
groundops=["", "", "Take", "", ""]
notedtemplate=799
invmodel=60067

# Holiday Mystery Box (placeholder)
[[item]]
id=33080
inherit=6199
name="Holiday Box"
placeholderid=32080
placeholdertemplate=14401
invmodel=60067

# 3rd Age Mystery Box
[[item]]
id=32209
inherit=6199
name="3rd Age Box"
placeholderid=32211
invmodel=60208
stackable=0
notedid=32210

# 3rd Age Mystery Box (noted)
[[item]]
id=32210
inherit=6200
name="3rd Age Box"
notedid=32209
op1=""
op2=""
op3=""
op4=""
op5="Drop"
groundops=["", "", "Take", "", ""]
notedtemplate=799
invmodel=60208

# 3rd Age Mystery Box (placeholder)
[[item]]
id=32211
inherit=6199
name="3rd Age Box"
placeholderid=32209
placeholdertemplate=14401
invmodel=60208

# Cosmetic Mystery box
[[item]]
inherit=6199
id=32163
name="Cosmetic Box"
grandexchange=true
invmodel=60157
placeholderid=33163

# Cosmetic Mystery box (placeholder)
[[item]]
inherit=16551
id=33163
placeholderid=32163
placeholdertemplate=14401

# Skilling Mystery Box
[[item]]
id=32212
inherit=6199
name="Skilling Box"
placeholderid=32214
invmodel=60209
stackable=0
notedid=32213
zoom=1780
modelroll=235
modelpitch=165
modelyaw=0
offsetx=0
offsety=-13

# Skilling Mystery Box (noted)
[[item]]
id=32213
inherit=6200
name="Skilling Box"
notedid=32212
op1=""
op2=""
op3=""
op4=""
op5="Drop"
groundops=["", "", "Take", "", ""]
notedtemplate=799
invmodel=60209

# Skilling Mystery Box (placeholder)
[[item]]
id=32214
inherit=6199
name="Skilling Box"
placeholderid=32212
placeholdertemplate=14401
invmodel=60209

[[item]]
id=32215
inherit=2724
name="Mystery Box Bundle"
op2=""

# Easter mystery box
[[item]]
id=32357
inherit=6199
name="Easter Mystery Box"
examine="A box containing a random Easter item."
invmodel=60290
grandexchange=true

# PvP Tourney mystery box
[[item]]
id=32368
inherit=6199
name="PVP Tourney Mystery Box"
examine="A box containing a random item."

# PVM Arena mystery box
[[item]]
id=32423
inherit=6199
name="PVM Arena mystery box"
examine="A box containing a random item."
invmodel=60290
grandexchange=true
placeholderid=32443
stackable=1

# PVM Arena mystery box (placeholder)
[[item]]
inherit=18086
id=32443
placeholderid=32423

# Pet mystery box
[[item]]
id=30031
inherit=6199
name="Pet Box"
grandexchange=true
placeholderid=30032
replacementcolours=[0]

# Pet mystery box (placeholder)
[[item]]
id=30032
inherit=18086
name="null"
placeholderid=30031
replacementcolours=[0]

# Pet mystery box rewards with destroy option
[[item]]
inherit=[20838, 20840, 20842, 20844, 11863, 12887, 12888, 12889, 12890,
    12891, 11021, 11019, 11020, 11022]
grandexchange=true
op5="Drop"